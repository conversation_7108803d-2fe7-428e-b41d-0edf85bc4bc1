import React, { FC, useEffect, useCallback, useState } from "react"
import { observer } from "mobx-react-lite"
import { ViewStyle, View } from "react-native"
import { AppStackScreenProps } from "app/navigators"
import { Screen, Text } from "app/components"
import { SubscriptionAnalyticsToggle } from "app/components/SubscriptionAnalyticsToggle"
import { SubscriptionAnalyticsList } from "app/components/SubscriptionAnalyticsList"
import { SubscriptionCalendarView } from "app/components/SubscriptionCalendarView"
import { colors, spacing, typography } from "app/theme"
import { useStores } from "app/models"
import { useAuth } from "app/contexts/AuthContext"
import { useFocusEffect } from "@react-navigation/native"
import { ErrorType, ErrorExperience, reportSentryError } from "app/utils/crashReporting"

interface UserAnalyticsScreenProps extends AppStackScreenProps<"UserAnalytics"> {}

type ViewMode = 'list' | 'calendar'

export const UserAnalyticsScreen: FC<UserAnalyticsScreenProps> = observer(function UserAnalyticsScreen() {
  const rootStore = useStores()
  const { userId } = useAuth()
  const [viewMode, setViewMode] = useState<ViewMode>('list')

  // Fetch subscriptions on mount and when userId changes
  useEffect(() => {
    if (userId) {
      // Only fetch if cache is invalid or no data exists
      if (!rootStore.isSubscriptionsCacheValid || rootStore.subscriptions.length === 0) {
        rootStore.fetchUserSubscriptions(userId).catch((error) => {
          if (__DEV__ && console.tron) {
            console.tron.error('❌ Failed to fetch subscriptions for analytics:', error)
          }
          reportSentryError(
            error instanceof Error ? error : new Error('Failed to fetch subscriptions for analytics'),
            ErrorType.HANDLED,
            ErrorExperience.DataRead
          )
        })
      }
    }
  }, [userId, rootStore.isSubscriptionsCacheValid])

  // Handle screen focus - refresh if cache has been busted
  useFocusEffect(
    useCallback(() => {
      if (userId && !rootStore.isSubscriptionsCacheValid) {
        if (__DEV__ && console.tron) {
          console.tron.log('📊 Analytics screen focused with invalid cache, refreshing subscriptions')
        }
        rootStore.fetchUserSubscriptions(userId).catch((error) => {
          if (__DEV__ && console.tron) {
            console.tron.error('❌ Failed to refresh subscriptions for analytics:', error)
          }
          reportSentryError(
            error instanceof Error ? error : new Error('Failed to refresh subscriptions for analytics'),
            ErrorType.HANDLED,
            ErrorExperience.DataRead
          )
        })
      }
    }, [userId, rootStore.isSubscriptionsCacheValid])
  )

  // Handle manual refresh
  const handleRefresh = useCallback(() => {
    if (userId) {
      if (__DEV__ && console.tron) {
        console.tron.log('📊 Manual refresh triggered in analytics')
      }
      rootStore.fetchUserSubscriptions(userId, true).catch((error) => {
        if (__DEV__ && console.tron) {
          console.tron.error('❌ Failed to manually refresh subscriptions for analytics:', error)
        }
        reportSentryError(
          error instanceof Error ? error : new Error('Failed to manually refresh subscriptions for analytics'),
          ErrorType.HANDLED,
          ErrorExperience.DataRead
        )
      })
    }
  }, [userId])

  const handleViewModeChange = useCallback((mode: ViewMode) => {
    if (__DEV__ && console.tron) {
      console.tron.log(`📊 Analytics view mode changed to: ${mode}`)
    }
    setViewMode(mode)
  }, [])

  return (
    <Screen style={$root} preset="fixed">
      <View style={$container}>
        <View style={$header}>
          <Text style={$title}>Subscription Analytics</Text>
          <SubscriptionAnalyticsToggle
            viewMode={viewMode}
            onViewModeChange={handleViewModeChange}
          />
        </View>

        {viewMode === 'list' ? (
          <SubscriptionAnalyticsList
            subscriptions={rootStore.subscriptions}
            loading={rootStore.subscriptionsLoading}
            onRefresh={handleRefresh}
          />
        ) : (
          <SubscriptionCalendarView
            subscriptions={rootStore.subscriptions}
            loading={rootStore.subscriptionsLoading}
            onRefresh={handleRefresh}
          />
        )}
      </View>
    </Screen>
  )
})

const $root: ViewStyle = {
  flex: 1,
  backgroundColor: colors.background,
}

const $container: ViewStyle = {
  flex: 1,
  paddingBottom: 80, // Space for bottom tab bar
}

const $header: ViewStyle = {
  paddingHorizontal: spacing.md,
  paddingTop: spacing.md,
  paddingBottom: spacing.sm,
  borderBottomWidth: 1,
  borderBottomColor: colors.separator,
  backgroundColor: colors.background,
}

const $title: ViewStyle = {
  fontSize: 24,
  fontFamily: typography.primary.bold,
  color: colors.text,
  marginBottom: spacing.sm,
}
