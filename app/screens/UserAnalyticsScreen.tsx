import React, { <PERSON> } from "react"
import { observer } from "mobx-react-lite"
import { ViewStyle, TextStyle } from "react-native"
import { AppStackScreenProps } from "app/navigators"
import { Screen, Text } from "app/components"
import { colors, spacing, typography } from "app/theme"

interface UserAnalyticsScreenProps extends AppStackScreenProps<"UserAnalytics"> {}

export const UserAnalyticsScreen: FC<UserAnalyticsScreenProps> = observer(function UserAnalyticsScreen() {
  return (
    <Screen style={$root} preset="scroll" contentContainerStyle={$scrollContainer}>
      <Text style={$title}>Subscription Analytics</Text>
      <Text style={$subtitle}>This is a test to see if basic text renders</Text>
    </Screen>
  )
})

const $root: ViewStyle = {
  flex: 1,
  backgroundColor: colors.background,
}

const $scrollContainer: ViewStyle = {
  padding: spacing.md,
  paddingBottom: 80, // Space for bottom tab bar
}

const $title: TextStyle = {
  fontSize: 24,
  fontFamily: typography.primary.bold,
  color: colors.text,
  marginBottom: spacing.sm,
}

const $subtitle: TextStyle = {
  fontSize: 16,
  fontFamily: typography.primary.normal,
  color: colors.textDim,
}
