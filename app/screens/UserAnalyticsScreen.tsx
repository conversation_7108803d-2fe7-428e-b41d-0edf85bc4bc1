import React, { FC, useEffect, useCallback, useState } from "react"
import { observer } from "mobx-react-lite"
import { ViewStyle, View, TextStyle } from "react-native"
import { AppStackScreenProps } from "app/navigators"
import { Screen, Text } from "app/components"
import { SubscriptionAnalyticsToggle } from "app/components/SubscriptionAnalyticsToggle"
import { SubscriptionAnalyticsList } from "app/components/SubscriptionAnalyticsList"
import { SubscriptionCalendarView } from "app/components/SubscriptionCalendarView"
import { colors, spacing, typography } from "app/theme"
import { useStores } from "app/models"
import { useAuth } from "app/contexts/AuthContext"
import { useFocusEffect } from "@react-navigation/native"
import { ErrorType, ErrorExperience, reportSentryError } from "app/utils/crashReporting"

interface UserAnalyticsScreenProps extends AppStackScreenProps<"UserAnalytics"> {}

type ViewMode = 'list' | 'calendar'

export const UserAnalyticsScreen: FC<UserAnalyticsScreenProps> = observer(function UserAnalyticsScreen() {
  const rootStore = useStores()
  const { userId } = useAuth()
  const [viewMode, setViewMode] = useState<ViewMode>('list')

  // Fetch subscriptions on mount and when userId changes
  useEffect(() => {
    if (userId) {
      // Only fetch if cache is invalid or no data exists
      if (!rootStore.isSubscriptionsCacheValid || rootStore.subscriptions.length === 0) {
        rootStore.fetchUserSubscriptions(userId).catch((error) => {
          if (__DEV__ && console.tron) {
            console.tron.error('❌ Failed to fetch subscriptions for analytics:', error)
          }
          reportSentryError(
            error instanceof Error ? error : new Error('Failed to fetch subscriptions for analytics'),
            ErrorType.HANDLED,
            ErrorExperience.DataRead
          )
        })
      }
    }
  }, [userId, rootStore.isSubscriptionsCacheValid])

  // Handle screen focus - refresh if cache has been busted
  useFocusEffect(
    useCallback(() => {
      if (userId && !rootStore.isSubscriptionsCacheValid) {
        if (__DEV__ && console.tron) {
          console.tron.log('📊 Analytics screen focused with invalid cache, refreshing subscriptions')
        }
        rootStore.fetchUserSubscriptions(userId).catch((error) => {
          if (__DEV__ && console.tron) {
            console.tron.error('❌ Failed to refresh subscriptions for analytics:', error)
          }
          reportSentryError(
            error instanceof Error ? error : new Error('Failed to refresh subscriptions for analytics'),
            ErrorType.HANDLED,
            ErrorExperience.DataRead
          )
        })
      }
    }, [userId, rootStore.isSubscriptionsCacheValid])
  )

  // Handle manual refresh
  const handleRefresh = useCallback(() => {
    if (userId) {
      if (__DEV__ && console.tron) {
        console.tron.log('📊 Manual refresh triggered in analytics')
      }
      rootStore.fetchUserSubscriptions(userId, true).catch((error) => {
        if (__DEV__ && console.tron) {
          console.tron.error('❌ Failed to manually refresh subscriptions for analytics:', error)
        }
        reportSentryError(
          error instanceof Error ? error : new Error('Failed to manually refresh subscriptions for analytics'),
          ErrorType.HANDLED,
          ErrorExperience.DataRead
        )
      })
    }
  }, [userId])

  const handleViewModeChange = useCallback((mode: ViewMode) => {
    if (__DEV__ && console.tron) {
      console.tron.log(`📊 Analytics view mode changed to: ${mode}`)
    }
    setViewMode(mode)
  }, [])

  // Debug logging
  if (__DEV__ && console.tron) {
    console.tron.log('📊 Analytics Screen Debug:', {
      userId,
      subscriptionsCount: rootStore.subscriptions.length,
      subscriptionsLoading: rootStore.subscriptionsLoading,
      isSubscriptionsCacheValid: rootStore.isSubscriptionsCacheValid,
      viewMode,
      subscriptions: rootStore.subscriptions.slice(0, 3).map(s => ({
        id: s.id,
        merchantName: s.merchantName,
        productName: s.productName,
        billDate: s.billDate
      }))
    })
  }

  return (
    <Screen style={$root} preset="fixed">
      <View style={$container}>
        <View style={$header}>
          <Text style={$title}>Subscription Analytics</Text>
          <SubscriptionAnalyticsToggle
            viewMode={viewMode}
            onViewModeChange={handleViewModeChange}
          />
        </View>

        <View style={$contentContainer}>
          <View style={$debugContainer}>
            <Text style={$debugText}>DEBUG INFO:</Text>
            <Text style={$debugText}>
              Subscriptions: {rootStore?.subscriptions?.length || 0}
            </Text>
            <Text style={$debugText}>
              Loading: {rootStore?.subscriptionsLoading ? 'Yes' : 'No'}
            </Text>
            <Text style={$debugText}>
              View Mode: {viewMode}
            </Text>
            <Text style={$debugText}>
              User ID: {userId || 'Not set'}
            </Text>
            <Text style={$debugText}>
              Cache Valid: {rootStore?.isSubscriptionsCacheValid ? 'Yes' : 'No'}
            </Text>
          </View>

          {rootStore.subscriptions.length > 0 ? (
            <View style={$listContainer}>
              <Text style={$listTitle}>Your Subscriptions</Text>
              {rootStore.subscriptions.slice(0, 5).map((subscription) => (
                <View key={subscription.id} style={$subscriptionRow}>
                  <Text style={$subscriptionName}>
                    {subscription.merchantName} - {subscription.productName}
                  </Text>
                  <Text style={$subscriptionDetails}>
                    Bill Date: {subscription.billDate || 'Not set'}
                  </Text>
                  <Text style={$subscriptionDetails}>
                    Cost: {subscription.cost > 0 ? `${subscription.currency} ${subscription.cost}` : 'Free'}
                  </Text>
                </View>
              ))}
            </View>
          ) : (
            <View style={$emptyContainer}>
              <Text style={$emptyTitle}>No Subscriptions Found</Text>
              <Text style={$emptyMessage}>
                Your subscription analytics will appear here once you add some subscriptions
              </Text>
            </View>
          )}
        </View>
      </View>
    </Screen>
  )
})

const $root: ViewStyle = {
  flex: 1,
  backgroundColor: colors.background,
}

const $container: ViewStyle = {
  flex: 1,
  paddingBottom: 80, // Space for bottom tab bar
}

const $header: ViewStyle = {
  paddingHorizontal: spacing.md,
  paddingTop: spacing.md,
  paddingBottom: spacing.sm,
  borderBottomWidth: 1,
  borderBottomColor: colors.separator,
  backgroundColor: colors.background,
}

const $contentContainer: ViewStyle = {
  flex: 1,
}

const $debugContainer: ViewStyle = {
  backgroundColor: colors.palette.neutral200,
  padding: spacing.md,
  margin: spacing.sm,
  borderRadius: 8,
  borderWidth: 1,
  borderColor: colors.palette.primary500,
}

const $debugText: TextStyle = {
  fontSize: 14,
  fontFamily: typography.primary.semiBold,
  color: colors.text,
  marginBottom: spacing.xs,
}

const $listContainer: ViewStyle = {
  flex: 1,
  padding: spacing.md,
}

const $listTitle: TextStyle = {
  fontSize: 18,
  fontFamily: typography.primary.semiBold,
  color: colors.text,
  marginBottom: spacing.md,
}

const $subscriptionRow: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  padding: spacing.sm,
  marginBottom: spacing.sm,
  borderRadius: 8,
}

const $subscriptionName: TextStyle = {
  fontSize: 16,
  fontFamily: typography.primary.semiBold,
  color: colors.text,
  marginBottom: spacing.xs,
}

const $subscriptionDetails: TextStyle = {
  fontSize: 14,
  fontFamily: typography.primary.normal,
  color: colors.textDim,
  marginBottom: 2,
}

const $emptyContainer: ViewStyle = {
  flex: 1,
  alignItems: 'center',
  justifyContent: 'center',
  paddingHorizontal: spacing.lg,
  paddingVertical: spacing.xxl,
}

const $emptyTitle: TextStyle = {
  fontSize: 20,
  fontFamily: typography.primary.semiBold,
  color: colors.text,
  textAlign: 'center',
  marginBottom: spacing.sm,
}

const $emptyMessage: TextStyle = {
  fontSize: 16,
  fontFamily: typography.primary.normal,
  color: colors.textDim,
  textAlign: 'center',
  lineHeight: 24,
}

const $title: TextStyle = {
  fontSize: 24,
  fontFamily: typography.primary.bold,
  color: colors.text,
  marginBottom: spacing.sm,
}
