import React, { FC, useEffect } from "react"
import { observer } from "mobx-react-lite"
import { ViewStyle, TextStyle, View, ScrollView } from "react-native"
import { AppStackScreenProps } from "app/navigators"
import { Screen, Text } from "app/components"
import { colors, spacing, typography } from "app/theme"
import { useStores } from "app/models"
import { useAuth } from "app/contexts/AuthContext"

interface UserAnalyticsScreenProps extends AppStackScreenProps<"UserAnalytics"> {}

export const UserAnalyticsScreen: FC<UserAnalyticsScreenProps> = observer(function UserAnalyticsScreen() {
  const rootStore = useStores()
  const { userId } = useAuth()

  // Fetch subscriptions on mount
  useEffect(() => {
    if (userId && rootStore.subscriptions.length === 0) {
      rootStore.fetchUserSubscriptions(userId)
    }
  }, [userId])

  return (
    <Screen style={$root} preset="fixed">
      <ScrollView style={$scrollView} contentContainerStyle={$scrollContainer}>
        <Text style={$title}>Subscription Analytics</Text>

        <View style={$debugSection}>
          <Text style={$debugTitle}>Debug Info:</Text>
          <Text style={$debugText}>User ID: {userId || 'Not set'}</Text>
          <Text style={$debugText}>Subscriptions: {rootStore.subscriptions.length}</Text>
          <Text style={$debugText}>Loading: {rootStore.subscriptionsLoading ? 'Yes' : 'No'}</Text>
        </View>

        {rootStore.subscriptions.length > 0 && (
          <View style={$subscriptionsSection}>
            <Text style={$sectionTitle}>Your Subscriptions:</Text>
            {rootStore.subscriptions.map((subscription) => (
              <View key={subscription.id} style={$subscriptionItem}>
                <Text style={$subscriptionName}>
                  {subscription.merchantName} - {subscription.productName}
                </Text>
                <Text style={$subscriptionDetail}>
                  Bill Date: {subscription.billDate || 'Not set'}
                </Text>
                <Text style={$subscriptionDetail}>
                  Cost: {subscription.cost > 0 ? `${subscription.currency} ${subscription.cost}` : 'Free'}
                </Text>
              </View>
            ))}
          </View>
        )}

        {rootStore.subscriptions.length === 0 && !rootStore.subscriptionsLoading && (
          <View style={$emptySection}>
            <Text style={$emptyText}>No subscriptions found</Text>
          </View>
        )}
      </ScrollView>
    </Screen>
  )
})

const $root: ViewStyle = {
  flex: 1,
  backgroundColor: colors.background,
}

const $scrollView: ViewStyle = {
  flex: 1,
}

const $scrollContainer: ViewStyle = {
  padding: spacing.md,
  paddingBottom: 80, // Space for bottom tab bar
}

const $title: TextStyle = {
  fontSize: 24,
  fontFamily: typography.primary.bold,
  color: colors.text,
  marginBottom: spacing.lg,
}

const $debugSection: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  padding: spacing.md,
  borderRadius: 8,
  marginBottom: spacing.lg,
}

const $debugTitle: TextStyle = {
  fontSize: 16,
  fontFamily: typography.primary.semiBold,
  color: colors.text,
  marginBottom: spacing.sm,
}

const $debugText: TextStyle = {
  fontSize: 14,
  fontFamily: typography.primary.normal,
  color: colors.textDim,
  marginBottom: spacing.xs,
}

const $subscriptionsSection: ViewStyle = {
  marginBottom: spacing.lg,
}

const $sectionTitle: TextStyle = {
  fontSize: 18,
  fontFamily: typography.primary.semiBold,
  color: colors.text,
  marginBottom: spacing.md,
}

const $subscriptionItem: ViewStyle = {
  backgroundColor: colors.palette.neutral50,
  padding: spacing.md,
  borderRadius: 8,
  marginBottom: spacing.sm,
}

const $subscriptionName: TextStyle = {
  fontSize: 16,
  fontFamily: typography.primary.semiBold,
  color: colors.text,
  marginBottom: spacing.xs,
}

const $subscriptionDetail: TextStyle = {
  fontSize: 14,
  fontFamily: typography.primary.normal,
  color: colors.textDim,
  marginBottom: 2,
}

const $emptySection: ViewStyle = {
  alignItems: 'center',
  paddingVertical: spacing.xxl,
}

const $emptyText: TextStyle = {
  fontSize: 16,
  fontFamily: typography.primary.normal,
  color: colors.textDim,
}
