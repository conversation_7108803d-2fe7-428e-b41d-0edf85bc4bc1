import React, { useCallback, useMemo } from "react"
import { observer } from "mobx-react-lite"
import { ScrollView, RefreshControl, View, ViewStyle, TextStyle } from "react-native"
import { Text } from "app/components"
import { colors, spacing, typography } from "app/theme"
import { UserSubscription } from "app/models/UserSubscription/UserSubscription"

export interface SubscriptionCalendarViewProps {
  subscriptions: UserSubscription[]
  loading: boolean
  onRefresh: () => void
}

interface CalendarDay {
  day: number
  subscriptions: UserSubscription[]
  isCurrentMonth: boolean
}

interface CalendarWeek {
  days: CalendarDay[]
}

export const SubscriptionCalendarView = observer(function SubscriptionCalendarView({
  subscriptions,
  loading,
  onRefresh
}: SubscriptionCalendarViewProps) {
  const currentDate = new Date()
  const currentMonth = currentDate.getMonth()
  const currentYear = currentDate.getFullYear()

  // Group subscriptions by bill date
  const subscriptionsByDate = useMemo(() => {
    const grouped: { [key: string]: UserSubscription[] } = {}
    
    subscriptions.forEach(subscription => {
      if (subscription.billDate) {
        const billDate = new Date(subscription.billDate)
        const day = billDate.getDate()
        const key = day.toString()
        
        if (!grouped[key]) {
          grouped[key] = []
        }
        grouped[key].push(subscription)
      }
    })
    
    return grouped
  }, [subscriptions])

  // Generate calendar weeks
  const calendarWeeks = useMemo(() => {
    const firstDayOfMonth = new Date(currentYear, currentMonth, 1)
    const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0)
    const startDate = new Date(firstDayOfMonth)
    startDate.setDate(startDate.getDate() - firstDayOfMonth.getDay())

    const weeks: CalendarWeek[] = []
    let currentWeekDate = new Date(startDate)

    while (currentWeekDate <= lastDayOfMonth || weeks.length === 0 || currentWeekDate.getDay() !== 0) {
      const week: CalendarWeek = { days: [] }
      
      for (let i = 0; i < 7; i++) {
        const day = currentWeekDate.getDate()
        const isCurrentMonth = currentWeekDate.getMonth() === currentMonth
        const daySubscriptions = isCurrentMonth ? (subscriptionsByDate[day.toString()] || []) : []
        
        week.days.push({
          day,
          subscriptions: daySubscriptions,
          isCurrentMonth
        })
        
        currentWeekDate.setDate(currentWeekDate.getDate() + 1)
      }
      
      weeks.push(week)
      
      if (currentWeekDate > lastDayOfMonth && currentWeekDate.getDay() === 0) {
        break
      }
    }

    return weeks
  }, [currentMonth, currentYear, subscriptionsByDate])

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ]

  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

  const renderCalendarDay = useCallback((calendarDay: CalendarDay) => {
    const isToday = calendarDay.isCurrentMonth && 
                   calendarDay.day === currentDate.getDate() &&
                   currentMonth === currentDate.getMonth() &&
                   currentYear === currentDate.getFullYear()

    return (
      <View key={calendarDay.day} style={$dayContainer}>
        <View style={[
          $dayHeader,
          isToday && $todayHeader,
          !calendarDay.isCurrentMonth && $otherMonthHeader
        ]}>
          <Text style={[
            $dayNumber,
            isToday && $todayText,
            !calendarDay.isCurrentMonth && $otherMonthText
          ]}>
            {calendarDay.day}
          </Text>
        </View>
        
        <View style={$dayContent}>
          {calendarDay.subscriptions.slice(0, 3).map((subscription, index) => (
            <View key={subscription.id} style={$subscriptionDot}>
              <Text style={$subscriptionText} numberOfLines={1}>
                {subscription.merchantName}
              </Text>
            </View>
          ))}
          {calendarDay.subscriptions.length > 3 && (
            <Text style={$moreText}>
              +{calendarDay.subscriptions.length - 3} more
            </Text>
          )}
        </View>
      </View>
    )
  }, [currentDate, currentMonth, currentYear])

  const renderWeek = useCallback((week: CalendarWeek, weekIndex: number) => (
    <View key={weekIndex} style={$weekContainer}>
      {week.days.map(renderCalendarDay)}
    </View>
  ), [renderCalendarDay])

  const ListEmptyComponent = useCallback(() => (
    <View style={$emptyContainer}>
      <Text style={$emptyTitle}>No Subscriptions Found</Text>
      <Text style={$emptyMessage}>
        Your subscription bill dates will appear on the calendar once you add some subscriptions
      </Text>
    </View>
  ), [])

  if (subscriptions.length === 0 && !loading) {
    return (
      <ScrollView
        style={$container}
        refreshControl={
          <RefreshControl
            refreshing={loading}
            onRefresh={onRefresh}
            tintColor={colors.tint}
            colors={[colors.tint]}
          />
        }
      >
        <ListEmptyComponent />
      </ScrollView>
    )
  }

  return (
    <ScrollView
      style={$container}
      refreshControl={
        <RefreshControl
          refreshing={loading}
          onRefresh={onRefresh}
          tintColor={colors.tint}
          colors={[colors.tint]}
        />
      }
    >
      <View style={$calendarContainer}>
        <View style={$monthHeader}>
          <Text style={$monthTitle}>
            {monthNames[currentMonth]} {currentYear}
          </Text>
        </View>

        <View style={$dayNamesContainer}>
          {dayNames.map(dayName => (
            <View key={dayName} style={$dayNameContainer}>
              <Text style={$dayNameText}>{dayName}</Text>
            </View>
          ))}
        </View>

        {calendarWeeks.map(renderWeek)}
      </View>
    </ScrollView>
  )
})

const $container: ViewStyle = {
  flex: 1,
}

const $calendarContainer: ViewStyle = {
  padding: spacing.md,
}

const $monthHeader: ViewStyle = {
  alignItems: 'center',
  marginBottom: spacing.md,
}

const $monthTitle: TextStyle = {
  fontSize: 20,
  fontFamily: typography.primary.bold,
  color: colors.text,
}

const $dayNamesContainer: ViewStyle = {
  flexDirection: 'row',
  marginBottom: spacing.xs,
}

const $dayNameContainer: ViewStyle = {
  flex: 1,
  alignItems: 'center',
  paddingVertical: spacing.xs,
}

const $dayNameText: TextStyle = {
  fontSize: 12,
  fontFamily: typography.primary.semiBold,
  color: colors.textDim,
  textTransform: 'uppercase',
}

const $weekContainer: ViewStyle = {
  flexDirection: 'row',
  marginBottom: spacing.xs,
}

const $dayContainer: ViewStyle = {
  flex: 1,
  minHeight: 80,
  marginHorizontal: 1,
  backgroundColor: colors.background,
  borderWidth: 1,
  borderColor: colors.separator,
}

const $dayHeader: ViewStyle = {
  alignItems: 'center',
  paddingVertical: spacing.xs,
  backgroundColor: colors.palette.neutral100,
}

const $todayHeader: ViewStyle = {
  backgroundColor: colors.palette.primary500,
}

const $otherMonthHeader: ViewStyle = {
  backgroundColor: colors.palette.neutral50,
}

const $dayNumber: TextStyle = {
  fontSize: 14,
  fontFamily: typography.primary.medium,
  color: colors.text,
}

const $todayText: TextStyle = {
  color: colors.palette.primary100,
  fontFamily: typography.primary.bold,
}

const $otherMonthText: TextStyle = {
  color: colors.textDim,
}

const $dayContent: ViewStyle = {
  flex: 1,
  padding: spacing.xs,
}

const $subscriptionDot: ViewStyle = {
  backgroundColor: colors.palette.primary200,
  borderRadius: 4,
  paddingHorizontal: spacing.xs,
  paddingVertical: 2,
  marginBottom: 2,
}

const $subscriptionText: TextStyle = {
  fontSize: 10,
  fontFamily: typography.primary.medium,
  color: colors.palette.primary700,
}

const $moreText: TextStyle = {
  fontSize: 10,
  fontFamily: typography.primary.normal,
  color: colors.textDim,
  fontStyle: 'italic',
}

const $emptyContainer: ViewStyle = {
  flex: 1,
  alignItems: 'center',
  justifyContent: 'center',
  paddingHorizontal: spacing.lg,
  paddingVertical: spacing.xxl,
  minHeight: 400,
}

const $emptyTitle: TextStyle = {
  fontSize: 20,
  fontFamily: typography.primary.semiBold,
  color: colors.text,
  textAlign: 'center',
  marginBottom: spacing.sm,
}

const $emptyMessage: TextStyle = {
  fontSize: 16,
  fontFamily: typography.primary.normal,
  color: colors.textDim,
  textAlign: 'center',
  lineHeight: 24,
}
