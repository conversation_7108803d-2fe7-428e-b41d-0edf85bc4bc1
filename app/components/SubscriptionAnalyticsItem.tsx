import React from "react"
import { observer } from "mobx-react-lite"
import { View, ViewStyle, TextStyle, ImageStyle } from "react-native"
import { Text, AutoImage } from "app/components"
import { colors, spacing, typography } from "app/theme"
import { UserSubscription } from "app/models/UserSubscription/UserSubscription"

export interface SubscriptionAnalyticsItemProps {
  subscription: UserSubscription
}

export const SubscriptionAnalyticsItem = observer(function SubscriptionAnalyticsItem({
  subscription
}: SubscriptionAnalyticsItemProps) {
  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-IE', {
      style: 'currency',
      currency: currency,
    }).format(amount)
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not set'
    return new Date(dateString).toLocaleDateString('en-IE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatBillDate = (dateString: string | null) => {
    if (!dateString) return 'Not set'
    return new Date(dateString).toLocaleDateString('en-IE', {
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <View style={$container}>
      <View style={$logoContainer}>
        {subscription.merchantLogo ? (
          <AutoImage
            source={{ uri: subscription.merchantLogo }}
            style={$logo}
            resizeMode="contain"
          />
        ) : (
          <View style={$logoPlaceholder}>
            <Text style={$logoPlaceholderText}>
              {subscription.merchantName.charAt(0).toUpperCase()}
            </Text>
          </View>
        )}
      </View>

      <View style={$contentContainer}>
        <View style={$row}>
          <View style={$merchantColumn}>
            <Text style={$merchantName} numberOfLines={1}>
              {subscription.merchantName}
            </Text>
            <Text style={$productName} numberOfLines={1}>
              {subscription.productName}
            </Text>
          </View>

          <View style={$billDateColumn}>
            <Text style={$columnHeader}>Bill Date</Text>
            <Text style={$billDate}>
              {formatBillDate(subscription.billDate)}
            </Text>
          </View>

          <View style={$costColumn}>
            <Text style={$columnHeader}>Cost</Text>
            {subscription.cost > 0 ? (
              <Text style={$cost}>
                {formatCurrency(subscription.cost, subscription.currency)}
              </Text>
            ) : (
              <Text style={$noCost}>-</Text>
            )}
          </View>

          <View style={$statusColumn}>
            <Text style={$columnHeader}>Status</Text>
            <View style={$statusContainer}>
              <View style={[
                $statusIndicator,
                subscription.isActive ? $statusActive : $statusInactive
              ]} />
              <Text style={[
                $statusText,
                subscription.isActive ? $statusActiveText : $statusInactiveText
              ]}>
                {subscription.isActive ? 'Active' : 'Inactive'}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </View>
  )
})

const $container: ViewStyle = {
  flexDirection: 'row',
  alignItems: 'center',
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
  backgroundColor: colors.background,
  borderBottomWidth: 1,
  borderBottomColor: colors.separator,
}

const $logoContainer: ViewStyle = {
  width: 40,
  height: 40,
  marginRight: spacing.sm,
}

const $logo: ImageStyle = {
  width: 40,
  height: 40,
  borderRadius: 8,
}

const $logoPlaceholder: ViewStyle = {
  width: 40,
  height: 40,
  borderRadius: 8,
  backgroundColor: colors.palette.neutral300,
  alignItems: 'center',
  justifyContent: 'center',
}

const $logoPlaceholderText: TextStyle = {
  fontSize: 16,
  fontFamily: typography.primary.semiBold,
  color: colors.palette.neutral600,
}

const $contentContainer: ViewStyle = {
  flex: 1,
}

const $row: ViewStyle = {
  flexDirection: 'row',
  alignItems: 'center',
}

const $merchantColumn: ViewStyle = {
  flex: 2,
  marginRight: spacing.sm,
}

const $billDateColumn: ViewStyle = {
  flex: 1,
  marginRight: spacing.sm,
}

const $costColumn: ViewStyle = {
  flex: 1,
  marginRight: spacing.sm,
}

const $statusColumn: ViewStyle = {
  flex: 1,
}

const $columnHeader: TextStyle = {
  fontSize: 12,
  fontFamily: typography.primary.medium,
  color: colors.textDim,
  marginBottom: 2,
}

const $merchantName: TextStyle = {
  fontSize: 16,
  fontFamily: typography.primary.semiBold,
  color: colors.text,
  marginBottom: 2,
}

const $productName: TextStyle = {
  fontSize: 14,
  fontFamily: typography.primary.normal,
  color: colors.textDim,
}

const $billDate: TextStyle = {
  fontSize: 14,
  fontFamily: typography.primary.medium,
  color: colors.text,
}

const $cost: TextStyle = {
  fontSize: 14,
  fontFamily: typography.primary.semiBold,
  color: colors.text,
}

const $noCost: TextStyle = {
  fontSize: 14,
  fontFamily: typography.primary.normal,
  color: colors.textDim,
}

const $statusContainer: ViewStyle = {
  flexDirection: 'row',
  alignItems: 'center',
}

const $statusIndicator: ViewStyle = {
  width: 8,
  height: 8,
  borderRadius: 4,
  marginRight: spacing.xs,
}

const $statusActive: ViewStyle = {
  backgroundColor: colors.palette.success500,
}

const $statusInactive: ViewStyle = {
  backgroundColor: colors.palette.neutral400,
}

const $statusText: TextStyle = {
  fontSize: 12,
  fontFamily: typography.primary.medium,
}

const $statusActiveText: TextStyle = {
  color: colors.palette.success500,
}

const $statusInactiveText: TextStyle = {
  color: colors.textDim,
}
