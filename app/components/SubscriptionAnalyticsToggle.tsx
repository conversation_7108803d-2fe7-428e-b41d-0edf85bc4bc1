import React from "react"
import { View, ViewStyle, TextStyle, Pressable } from "react-native"
import { Text } from "app/components"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { faList, faCalendarAlt } from "@fortawesome/free-solid-svg-icons"
import { colors, spacing, typography } from "app/theme"

export interface SubscriptionAnalyticsToggleProps {
  viewMode: 'list' | 'calendar'
  onViewModeChange: (mode: 'list' | 'calendar') => void
}

export function SubscriptionAnalyticsToggle({
  viewMode,
  onViewModeChange
}: SubscriptionAnalyticsToggleProps) {
  return (
    <View style={$container}>
      <Pressable
        style={[
          $toggleButton,
          $leftButton,
          viewMode === 'list' && $activeButton
        ]}
        onPress={() => onViewModeChange('list')}
      >
        <FontAwesomeIcon
          icon={faList}
          size={16}
          color={viewMode === 'list' ? colors.palette.primary100 : colors.textDim}
        />
        <Text style={[
          $buttonText,
          viewMode === 'list' && $activeButtonText
        ]}>
          List
        </Text>
      </Pressable>

      <Pressable
        style={[
          $toggleButton,
          $rightButton,
          viewMode === 'calendar' && $activeButton
        ]}
        onPress={() => onViewModeChange('calendar')}
      >
        <FontAwesomeIcon
          icon={faCalendarAlt}
          size={16}
          color={viewMode === 'calendar' ? colors.palette.primary100 : colors.textDim}
        />
        <Text style={[
          $buttonText,
          viewMode === 'calendar' && $activeButtonText
        ]}>
          Calendar
        </Text>
      </Pressable>
    </View>
  )
}

const $container: ViewStyle = {
  flexDirection: 'row',
  backgroundColor: colors.palette.neutral200,
  borderRadius: 8,
  padding: 2,
}

const $toggleButton: ViewStyle = {
  flex: 1,
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'center',
  paddingVertical: spacing.xs,
  paddingHorizontal: spacing.sm,
  borderRadius: 6,
}

const $leftButton: ViewStyle = {
  marginRight: 1,
}

const $rightButton: ViewStyle = {
  marginLeft: 1,
}

const $activeButton: ViewStyle = {
  backgroundColor: colors.palette.primary500,
}

const $buttonText: TextStyle = {
  fontSize: 14,
  fontFamily: typography.primary.medium,
  color: colors.textDim,
  marginLeft: spacing.xs,
}

const $activeButtonText: TextStyle = {
  color: colors.palette.primary100,
}
