import React, { useCallback } from "react"
import { observer } from "mobx-react-lite"
import { FlatList, RefreshControl, View, ViewStyle, TextStyle } from "react-native"
import { Text } from "app/components"
import { SubscriptionAnalyticsItem } from "./SubscriptionAnalyticsItem"
import { colors, spacing, typography } from "app/theme"
import { UserSubscription } from "app/models/UserSubscription/UserSubscription"

export interface SubscriptionAnalyticsListProps {
  subscriptions: UserSubscription[]
  loading: boolean
  onRefresh: () => void
}

export const SubscriptionAnalyticsList = observer(function SubscriptionAnalyticsList({
  subscriptions,
  loading,
  onRefresh
}: SubscriptionAnalyticsListProps) {
  // Debug logging
  if (__DEV__ && console.tron) {
    console.tron.log('📊 SubscriptionAnalyticsList Debug:', {
      subscriptionsCount: subscriptions.length,
      loading,
      subscriptions: subscriptions.slice(0, 2).map(s => ({
        id: s.id,
        merchantName: s.merchantName,
        productName: s.productName
      }))
    })
  }
  const renderItem = useCallback(({ item }: { item: UserSubscription }) => (
    <SubscriptionAnalyticsItem subscription={item} />
  ), [])

  const keyExtractor = useCallback((item: UserSubscription) => item.id, [])

  const getItemLayout = useCallback((data: any, index: number) => ({
    length: 80, // Approximate height of SubscriptionAnalyticsItem
    offset: 80 * index,
    index,
  }), [])

  const ItemSeparatorComponent = useCallback(() => (
    <View style={$separator} />
  ), [])

  const ListHeaderComponent = useCallback(() => (
    <View style={$headerContainer}>
      <View style={$headerRow}>
        <View style={$merchantHeaderColumn}>
          <Text style={$headerText}>Subscription</Text>
        </View>
        <View style={$billDateHeaderColumn}>
          <Text style={$headerText}>Bill Date</Text>
        </View>
        <View style={$costHeaderColumn}>
          <Text style={$headerText}>Cost</Text>
        </View>
        <View style={$statusHeaderColumn}>
          <Text style={$headerText}>Status</Text>
        </View>
      </View>
    </View>
  ), [])

  const ListEmptyComponent = useCallback(() => (
    <View style={$emptyContainer}>
      <Text style={$emptyTitle}>No Subscriptions Found</Text>
      <Text style={$emptyMessage}>
        Your subscription analytics will appear here once you add some subscriptions
      </Text>
    </View>
  ), [])

  if (subscriptions.length === 0 && !loading) {
    return (
      <View style={$container}>
        <ListHeaderComponent />
        <ListEmptyComponent />
      </View>
    )
  }

  return (
    <View style={$container}>
      <FlatList
        data={subscriptions}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        getItemLayout={getItemLayout}
        ItemSeparatorComponent={ItemSeparatorComponent}
        ListHeaderComponent={ListHeaderComponent}
        refreshControl={
          <RefreshControl
            refreshing={loading}
            onRefresh={onRefresh}
            tintColor={colors.tint}
            colors={[colors.tint]}
          />
        }
        showsVerticalScrollIndicator={false}
        contentContainerStyle={$contentContainer}
      />
    </View>
  )
})

const $container: ViewStyle = {
  flex: 1,
}

const $contentContainer: ViewStyle = {
  flexGrow: 1,
}

const $separator: ViewStyle = {
  height: 1,
  backgroundColor: colors.separator,
  marginLeft: spacing.md + 40 + spacing.sm, // Align with content after logo
}

const $headerContainer: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderBottomWidth: 2,
  borderBottomColor: colors.separator,
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
}

const $headerRow: ViewStyle = {
  flexDirection: 'row',
  alignItems: 'center',
  marginLeft: 40 + spacing.sm, // Align with content after logo space
}

const $merchantHeaderColumn: ViewStyle = {
  flex: 2,
  marginRight: spacing.sm,
}

const $billDateHeaderColumn: ViewStyle = {
  flex: 1,
  marginRight: spacing.sm,
}

const $costHeaderColumn: ViewStyle = {
  flex: 1,
  marginRight: spacing.sm,
}

const $statusHeaderColumn: ViewStyle = {
  flex: 1,
}

const $headerText: TextStyle = {
  fontSize: 14,
  fontFamily: typography.primary.semiBold,
  color: colors.text,
  textTransform: 'uppercase',
  letterSpacing: 0.5,
}

const $emptyContainer: ViewStyle = {
  flex: 1,
  alignItems: 'center',
  justifyContent: 'center',
  paddingHorizontal: spacing.lg,
  paddingVertical: spacing.xxl,
}

const $emptyTitle: TextStyle = {
  fontSize: 20,
  fontFamily: typography.primary.semiBold,
  color: colors.text,
  textAlign: 'center',
  marginBottom: spacing.sm,
}

const $emptyMessage: TextStyle = {
  fontSize: 16,
  fontFamily: typography.primary.normal,
  color: colors.textDim,
  textAlign: 'center',
  lineHeight: 24,
}
